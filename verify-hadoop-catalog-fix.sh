#!/bin/bash

echo "=== 验证Hadoop Catalog修复 ==="
echo ""

echo "1. 检查TPCDSDatagen.scala中的修改："
echo "   查找硬编码的hive配置..."
if grep -n "spark_catalog.*type.*hive" src/main/scala/org/apache/spark/sql/execution/benchmark/TPCDSDatagen.scala; then
    echo "   ❌ 仍然存在硬编码的hive配置"
else
    echo "   ✅ 已移除硬编码的hive配置"
fi

echo ""
echo "   查找动态catalog类型配置..."
if grep -n "datagenArgs.icebergCatalogType" src/main/scala/org/apache/spark/sql/execution/benchmark/TPCDSDatagen.scala; then
    echo "   ✅ 已添加动态catalog类型配置"
else
    echo "   ❌ 缺少动态catalog类型配置"
fi

echo ""
echo "2. 检查TPCDSQueryBenchmark.scala中的修改："
echo "   查找硬编码的hive配置..."
if grep -n "spark_catalog.*type.*hive" src/main/scala/org/apache/spark/sql/execution/benchmark/TPCDSQueryBenchmark.scala; then
    echo "   ❌ 仍然存在硬编码的hive配置"
else
    echo "   ✅ 已移除硬编码的hive配置"
fi

echo ""
echo "   查找动态catalog配置..."
if grep -n "benchmarkArgs.icebergCatalogType" src/main/scala/org/apache/spark/sql/execution/benchmark/TPCDSQueryBenchmark.scala; then
    echo "   ✅ 已添加动态catalog配置"
else
    echo "   ❌ 缺少动态catalog配置"
fi

echo ""
echo "3. 显示关键修改内容："
echo ""
echo "=== TPCDSDatagen.scala 关键行 ==="
grep -n -A 5 -B 5 "spark_catalog.*type" src/main/scala/org/apache/spark/sql/execution/benchmark/TPCDSDatagen.scala

echo ""
echo "=== TPCDSQueryBenchmark.scala 关键行 ==="
grep -n -A 5 -B 5 "spark_catalog.*type" src/main/scala/org/apache/spark/sql/execution/benchmark/TPCDSQueryBenchmark.scala

echo ""
echo "4. 修改总结："
echo "   - 移除了spark_catalog的硬编码hive类型"
echo "   - 添加了基于用户选择的动态catalog类型配置"
echo "   - 为hadoop catalog添加了warehouse配置"
echo "   - 为hive catalog保留了metastore URI配置"

echo ""
echo "5. 使用建议："
echo "   现在可以安全使用hadoop catalog，不会再连接Hive Metastore"
echo "   使用命令："
echo "   ./bin/generate-pb-scale-data \\"
echo "     --scale-factor 1000 \\"
echo "     --iceberg-catalog-type hadoop \\"
echo "     --warehouse-location hdfs://NS1/bdoc/data/iceberg-warehouse \\"
echo "     --output-location hdfs://NS1/bdoc/data/iceberg-warehouse/tpcds_data"

echo ""
echo "=== 验证完成 ==="
